#ifndef __ADC_APP_H_
#define __ADC_APP_H_

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// ADC采样频率枚举
typedef enum {
    ADC_FREQ_10HZ = 0,
    ADC_FREQ_100HZ = 1
} adc_freq_t;

// 函数声明
void adc_task(void);
float adc_get_voltage(void);
void adc_set_frequency(adc_freq_t freq);
adc_freq_t adc_get_frequency(void);
uint16_t adc_get_raw_value(void);

#ifdef __cplusplus
}
#endif

#endif /* __ADC_APP_H_ */
