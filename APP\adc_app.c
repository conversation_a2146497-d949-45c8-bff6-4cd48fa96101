#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];
extern uint16_t convertarr[CONVERT_NUM];

// ADC相关全局变量
static adc_freq_t current_freq = ADC_FREQ_10HZ; // 默认10Hz
static float current_voltage = 0.0f;
static uint32_t last_sample_time = 0;

/**
 * @brief ADC任务函数
 * 根据设定的采样频率进行ADC采样
 */
void adc_task(void)
{
    uint32_t current_time = get_system_ms();
    uint32_t sample_interval;

    // 根据采样频率设置采样间隔
    if (current_freq == ADC_FREQ_10HZ)
    {
        sample_interval = 100; // 100ms间隔，10Hz
    }
    else
    {
        sample_interval = 10; // 10ms间隔，100Hz
    }

    // 检查是否到了采样时间
    if (current_time - last_sample_time >= sample_interval)
    {
        // 更新ADC值
        convertarr[0] = adc_value[0];

        // 计算电压值 (0~3.3V对应0~4095)
        current_voltage = (float)adc_value[0] * 3.3f / 4095.0f;

        last_sample_time = current_time;
    }
}

/**
 * @brief 获取当前电压值
 * @return 电压值(V)
 */
float adc_get_voltage(void)
{
    return current_voltage;
}

/**
 * @brief 设置ADC采样频率
 * @param freq 采样频率
 */
void adc_set_frequency(adc_freq_t freq)
{
    current_freq = freq;
}

/**
 * @brief 获取当前ADC采样频率
 * @return 当前采样频率
 */
adc_freq_t adc_get_frequency(void)
{
    return current_freq;
}

/**
 * @brief 获取ADC原始值
 * @return ADC原始值
 */
uint16_t adc_get_raw_value(void)
{
    return adc_value[0];
}
